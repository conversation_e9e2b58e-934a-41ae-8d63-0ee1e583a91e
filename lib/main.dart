import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

import 'models/saga_model.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'One Piece Sagas',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const SagaListScreen(),
    );
  }
}

class SagaListScreen extends StatelessWidget {
  const SagaListScreen({super.key});

  Future<List<SagaModel>> fetchSaga() async {
    final response = await http.get(
      Uri.parse('https://api-onepiece.com/sagas'),
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data.map((json) => SagaModel.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load sagas');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('One Piece Sagas'),
      ),
      body: FutureBuilder<List<SagaModel>>(
        future: fetchSaga(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator()); // Loading spinner
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}')); // Show error
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No sagas found')); // No data message
          } else {
            final sagas = snapshot.data!;
            return ListView.builder(
              itemCount: sagas.length,
              itemBuilder: (context, index) {
                final saga = sagas[index];
                return ListTile(
                  title: Text(saga.name),
                  subtitle: Text(
                    'Saga Number: ${saga.sagaNumber ?? 'N/A'}\n'
                    'Volumes: ${saga.sagaValue ?? 'N/A'}\n'
                    'Chapters: ${saga.sagaChapiter}\n'
                    'Episodes: ${saga.sagaEpisode}',
                  ),
                );
              },
            );
          }
        },
      ),
    );
  }
}
