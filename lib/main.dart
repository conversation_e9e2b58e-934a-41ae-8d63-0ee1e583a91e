import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'bagzz.dart';
import 'core/routes/app_router.dart';

void main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations for better performance
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Run the app
  runApp(Bagzz(
    appRouter: AppRouter(),
  ));
}