import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'font_weight_helper.dart';


class Styles {
  static  TextStyle font22BlackBold = TextStyle(
    fontSize: 22.sp,
    fontWeight: FontWeightHelper.bold,
    color: Colors.black,
    fontFamily: 'PlayfairDisplay',

  );
  static TextStyle font18BlackBold = TextStyle(
    fontSize: 18.sp,
    fontWeight: FontWeightHelper.bold,
    color: Colors.black,
    fontFamily: 'PlayfairDisplay',
  );
  static TextStyle font16BlackMedium =TextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeightHelper.medium,
    color: Colors.black,
    fontFamily: 'WorkSans',
  );
  static TextStyle font16WhiteBold =TextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeightHelper.bold,
    color: Colors.white,
    fontFamily: 'WorkSans',
  );
  static TextStyle font24BlackBold =TextStyle(
    fontSize: 24.sp,
    fontWeight: FontWeightHelper.bold,
    color: Colors.black,
    fontFamily: 'PlayfairDisplay',
  );
  static TextStyle font14BlackMedium =TextStyle(
    fontSize: 14.sp,
    fontWeight: FontWeightHelper.regular,
    color: Colors.black,
    fontFamily: 'WorkSans',
  );
  static TextStyle font12Greylight =TextStyle(
    fontSize: 12.sp,
    fontWeight: FontWeightHelper.light,
    color: Colors.grey,
    fontFamily: 'WorkSans',
  );
  static TextStyle font14WhiteMedium =TextStyle(
    fontSize: 14.sp,
    fontWeight: FontWeightHelper.medium,
    color: Colors.white,
    fontFamily: 'WorkSans',
  );
  static TextStyle font16BlackBold =TextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeightHelper.bold,
    color: Colors.black,
    fontFamily: 'WorkSans',
  );
  static TextStyle font21BlackRegular =TextStyle(
    fontSize: 21.sp,
    fontWeight: FontWeightHelper.regular,
    color: Color(0xff000000),
    fontFamily: 'WorkSans',
  );
  static TextStyle font10BlackThin =TextStyle(
    fontSize: 10.sp,
    fontWeight: FontWeightHelper.thin,
    color: Color(0xff000000),
    fontFamily: 'WorkSans',
  );

 }