
import 'package:bagzz/core/routes/routes.dart';
import 'package:flutter/material.dart';

import '../../features/home/<USER>/home_screen.dart';
import '../../features/product_details/ui/search_screen.dart';

class AppRouter {

  Route onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case Routes.home:
        return MaterialPageRoute(builder: (context) => HomeScreen());
      case Routes.product:
        return MaterialPageRoute(builder: (context) => SearchScreen());
      default:
        return MaterialPageRoute(builder: (context) => Placeholder(
          child: Center(child: Text("there is no route for ${settings.name}")),
        ));
    }
  }

}
