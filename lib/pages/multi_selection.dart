import 'package:flutter/material.dart';

class MultiSelection extends StatefulWidget {
  const MultiSelection({super.key});

  @override
  State<MultiSelection> createState() => _MultiSelectionState();
}

class _MultiSelectionState extends State<MultiSelection> {
  List<Map<String, dynamic>> images = [
    {'path': 'assets/images/Ace.png', 'name': 'Ace', 'color': Colors.blueGrey},
    {
      'path': 'assets/images/crok.png',
      'name': 'Crok',
      'color': Colors.pinkAccent,
    },
    {
      'path': 'assets/images/kiizaro.jpg',
      'name': '<PERSON><PERSON><PERSON>',
      'color': Colors.deepPurple,
    },
    {'path': 'assets/images/logo.png', 'name': 'logo', 'color': Colors.black},
    {'path': 'assets/images/luffy.jpg', 'name': 'luffy', 'color': Colors.amber},
    {
      'path': 'assets/images/OP.png',
      'name': 'onepiece',
      'color': Colors.red.shade900,
    },
  ];

  List<int> selectedIndices = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.teal,
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Expanded(
              child: GridView.builder(
                itemCount: images.length,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 10,
                ),
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        // selectedIndex = index;
                        // scaffoldColor = images[index]['color'];
                      });
                    },
                    child: Stack(
                      children: [
                        // selectedIndex == index
                        Padding(
                          padding: const EdgeInsets.only(top: 20),
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Colors.black12,
                                  Colors.black26,
                                  Colors.black12,
                                ],
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),

                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            image: DecorationImage(
                              image: AssetImage(images[index]['path']),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        //     : Container(
                        //   decoration: BoxDecoration(
                        //     border: Border.all(color: Colors.black, width: 4),
                        //     borderRadius: BorderRadius.circular(10),
                        //     image: DecorationImage(
                        //       image: AssetImage(images[index]['path']),
                        //       fit: BoxFit.cover,
                        //     ),
                        //   ),
                        //   child: Align(
                        //     alignment: Alignment.bottomCenter,
                        //     child: Container(
                        //       decoration: BoxDecoration(
                        //         color: Colors.black.withOpacity(0.5),
                        //         borderRadius: BorderRadius.only(
                        //           bottomLeft: Radius.circular(10),
                        //           bottomRight: Radius.circular(10),
                        //         ),
                        //       ),
                        //       child: Padding(
                        //         padding: const EdgeInsets.symmetric(
                        //           horizontal: 8.0,
                        //         ),
                        //         child: Text(
                        //           images[index]['name'],
                        //           style: TextStyle(
                        //             color: Colors.white,
                        //             fontSize: 18,
                        //             fontWeight: FontWeight.bold,
                        //           ),
                        //         ),
                        //       ),
                        //     ),
                        //   ),
                        // ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
