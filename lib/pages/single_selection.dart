import 'package:flutter/material.dart';

class SingleSelection extends StatefulWidget {
  const SingleSelection({super.key});

  @override
  State<SingleSelection> createState() => _SingleSelectionState();
}

class _SingleSelectionState extends State<SingleSelection> {
  int selectedIndex = 0;

  Color scaffoldColor = Colors.brown;

  // List of images
  List<Map<String, dynamic>> images = [
    {'path': 'assets/images/Ace.png', 'name': 'Ace' ,'color': Colors.blueGrey},
    {'path': 'assets/images/crok.png', 'name': 'Crok','color': Colors.pinkAccent},
    {'path': 'assets/images/kiizaro.jpg', 'name': '<PERSON><PERSON><PERSON>','color': Colors.deepPurple},
    {'path': 'assets/images/logo.png', 'name': 'logo','color': Colors.black},
    {'path': 'assets/images/luffy.jpg', 'name': 'luffy','color': Colors.amber},
    {'path': 'assets/images/OP.png', 'name': 'onepiece','color': Colors.red.shade900},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: scaffoldColor,
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Image Grid
            Expanded(
              child: GridView.builder(
                itemCount: images.length,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 10,
                ),
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        selectedIndex = index;
                        scaffoldColor = images[index]['color'];
                      });
                    },
                    child: Stack(
                      children: [
                        selectedIndex == index
                            ? Padding(
                              padding: const EdgeInsets.only(top: 20),
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      Colors.black12,
                                      Colors.black26,
                                      Colors.black12,
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            )
                            : SizedBox.shrink(),

                        selectedIndex == index
                            ? Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                image: DecorationImage(
                                  image: AssetImage(images[index]['path']),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            )
                            : Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.black, width: 4),
                                borderRadius: BorderRadius.circular(10),
                                image: DecorationImage(
                                  image: AssetImage(images[index]['path']),
                                  fit: BoxFit.cover,
                                ),
                              ),
                              child: Align(
                                alignment: Alignment.bottomCenter,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.black.withOpacity(0.5),
                                    borderRadius: BorderRadius.only(
                                      bottomLeft: Radius.circular(10),
                                      bottomRight: Radius.circular(10),
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8.0,
                                    ),
                                    child: Text(
                                      images[index]['name'],
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                      ],
                    ),
                  );
                },
              ),
            ),

            SizedBox(height: 20),

            Row(
              children: [
                Image.asset(
                  images[selectedIndex]['path'],
                  height: 100,
                ),
                SizedBox(width: 10),
                Text(
                  images[selectedIndex]['name'],
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),



            SizedBox(height: 20),



            // Shuffle Button
            GestureDetector(
              onTap: () {
                setState(() {
                  images.shuffle();
                });
              },
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: Colors.grey.withOpacity(0.6),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Shuffle',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(width: 10),
                      Icon(Icons.shuffle),
                    ],
                  ),
                ),
              ),
            ),

            SizedBox(height: 20),

          ],
        ),
      ),
    );
  }
}
