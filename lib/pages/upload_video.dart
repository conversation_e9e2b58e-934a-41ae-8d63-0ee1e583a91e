import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:video_player/video_player.dart';

class UplaodVideo extends StatefulWidget {
  const UplaodVideo({super.key});

  @override
  State<UplaodVideo> createState() => _UplaodVideoState();
}

class _UplaodVideoState extends State<UplaodVideo> {

  File? _videoFile;
  VideoPlayerController? _controller;


  Future<void> _pickVideo() async {
    final pickedFile = await ImagePicker().pickVideo(source: ImageSource.gallery);
    if (pickedFile != null) {
      _videoFile = File(pickedFile.path);
      _controller = VideoPlayerController.file(_videoFile!)
        ..initialize().then((_) {
          setState(() {});
          _controller!.play();
        });
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.blueGrey.shade900,
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(

          children: [

            // Upload Video
            SizedBox(height: 40),
            Text(
              'Upload Video',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            // Upload Video
            SizedBox(height: 20),
            GestureDetector(

              onTap: _pickVideo,

              child: Center(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Colors.grey.withOpacity(0.6),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Upload Video',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(width: 10),
                        Icon(Icons.upload),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(height: 20),


            Expanded(child: SingleChildScrollView(
              child: Column(
                children: [
                  //view video
                  if (_controller != null)
                    AspectRatio(
                      aspectRatio: _controller!.value.aspectRatio,
                      child: VideoPlayer(_controller!,
                      ),
                    ),
                  if (_controller != null)
                    VideoProgressIndicator(_controller!, allowScrubbing: true),
                  if (_controller != null)
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _controller!.value.isPlaying
                              ? _controller!.pause()
                              : _controller!.play();
                        });
                      },
                      icon: Icon(
                        _controller!.value.isPlaying ? Icons.pause : Icons.play_arrow,
                      ),
                    ),
                ],
              ),
            ),

      ),


          ],
        ),
      )
    );
  }
}
