import 'package:flutter/material.dart';
import 'package:untitled/pages/image_selection.dart';
import 'package:untitled/pages/multi_selection.dart';
import 'package:untitled/pages/single_selection.dart';

class Docdoc extends StatefulWidget {
  Docdoc({super.key});

  @override
  State<Docdoc> createState() => _DocdocState();
}

class _DocdocState extends State<Docdoc> {

  int selectedPage = 0;
  final PageController controller = PageController();
  List<Widget> page = [
    SingleSelection(),
    MultiSelection(),
    ImageSelection(),
  ];

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        body: Column(
          children: [
            Expanded(
              flex:9,
              child: PageView(
                controller: controller,
                physics:  NeverScrollableScrollPhysics(),
                children: page,
                onPageChanged: (value) {
                  setState(() {
                    selectedPage = value;
                  });
                },
              ),
            ),
            Expanded(
              flex: 1,
              child: Container(

                color: Colors.grey.withOpacity(0.6),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [


                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Container(
                        decoration:BoxDecoration(
                          border: Border.all(color: Colors.white,width: 2),
                         borderRadius:BorderRadius.circular(10),
                          color: selectedPage == 0 ? Colors.blue.withOpacity(0.7) : Colors.grey.withOpacity(0.6),
                        ),
                        child: IconButton(
                          onPressed: () {
                            setState(() {
                              controller.jumpToPage(selectedPage - 1);
                            });
                          },
                          icon: Icon(Icons.arrow_back),
                        ),
                      ),
                    ),

                    SizedBox(width: 10,),

                    Container(
                      decoration:BoxDecoration(
                        borderRadius:BorderRadius.circular(10),
                        color: selectedPage == 1 ? Color(0xff62D2C3): Colors.grey.withOpacity(0.6),
                      ),
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            controller.jumpToPage(selectedPage + 1);
                          });
                        },
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(width: 10,),
                            Text('Next',style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),),
                            IconButton(
                              onPressed: () {
                                setState(() {
                                  controller.jumpToPage(selectedPage + 1);
                                });
                                },
                              icon: Icon(Icons.arrow_forward),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                )
              ),
            ),

          ],
        ),
      ),
    );
  }
}
