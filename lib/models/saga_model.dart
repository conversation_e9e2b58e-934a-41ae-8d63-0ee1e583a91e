class SagaModel {
  final int id;
  final String name;
  final int? sagaNumber;
  final int? sagaValue;
  final String sagaChapiter;
  final String sagaEpisode;

  SagaModel({
    required this.id,
    required this.name,
    required this.sagaNumber,
    required this.sagaValue,
    required this.sagaChapiter,
    required this.sagaEpisode,
  });

  factory SagaModel.fromJson(Map<String, dynamic> json) {
    return SagaModel(
      id: json['id'],
      name: json['title'],
      sagaNumber: json['saga_number'],
      sagaValue: json['saga_volume'],
      sagaChapiter: json['saga_chapitre'],
      sagaEpisode: json['saga_episode'],
    );
  }
}
