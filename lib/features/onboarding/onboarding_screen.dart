import 'package:doctor/features/onboarding/widgets/doc_logo_and_name.dart';
import 'package:doctor/features/onboarding/widgets/doctor_image_and_text.dart';
import 'package:doctor/features/onboarding/widgets/get_started_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/theming/styles.dart';

class OnboardingScreen extends StatelessWidget {
  const OnboardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.only(bottom: 30.h, top: 30.h),
            child: <PERSON>umn(
              children: [
                const DocLogoAndName(),
                <PERSON><PERSON><PERSON><PERSON>(height: 30.h),
                const DoctorImageAndText(),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.h),
                  child: Column(
                    children: [
                      Text(
                        'Manage and schedule all of your medical appointments easily with Docdoc to get a new experience.',
                        style: TextStyles.font13GrayRegular,
                        textAlign: TextAlign.center,
                      ),
                      <PERSON><PERSON><PERSON><PERSON>(height: 30.h),
                      const GetStartedButton(),

                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
