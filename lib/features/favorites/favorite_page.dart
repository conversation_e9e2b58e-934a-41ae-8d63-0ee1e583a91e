import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';

import '../../core/helpers/spacing.dart';
import '../../core/theming/styles.dart';
import 'provider/favorites_provider.dart';

class FavoritePage extends StatelessWidget {
  const FavoritePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text('My Favorites', style: Styles.font22BlackBold),
      ),
      body: Consumer<FavoritesProvider>(
        builder: (context, favoritesProvider, child) {
          final favorites = favoritesProvider.favorites;
          
          if (favorites.isEmpty) {
            return _buildEmptyState();
          }
          
          return ListView.builder(
            itemCount: favorites.length,
            itemBuilder: (context, index) {
              final item = favorites[index];
              return _buildFavoriteItem(
                context: context,
                item: item,
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            'assets/svgs/heart.svg',
            height: 60.h,
            color: Colors.grey,
          ),
          Spacing.verticalSpace(20),
          Text(
            'No favorites yet',
            style: Styles.font18BlackBold,
          ),
          Spacing.verticalSpace(10),
          Text(
            'Items added to your favorites will appear here',
            style: Styles.font14BlackMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFavoriteItem({
    required BuildContext context,
    required FavoriteItem item,
  }) {
    final favoritesProvider = Provider.of<FavoritesProvider>(context, listen: false);
    
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          // Product image and details section
          Row(
            children: [
              Image.asset(item.image, height: 100.h),
              Spacing.horizontalSpace(10),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(item.title, style: Styles.font22BlackBold),
                  Text(item.price, style: Styles.font16BlackBold),
                  Text(
                    item.bagStyle,
                    style: Styles.font10GreyBold,
                  ),
                ],
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  favoritesProvider.removeFromFavorites(item.id);
                  
                  // Show feedback
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('Removed from favorites'),
                      action: SnackBarAction(
                        label: 'UNDO',
                        onPressed: () {
                          favoritesProvider.toggleFavorite(
                            id: item.id,
                            image: item.image,
                            title: item.title,
                            price: item.price,
                            bagStyle: item.bagStyle,
                          );
                        },
                      ),
                    ),
                  );
                },
                child: SvgPicture.asset(
                  'assets/svgs/heart.svg',
                  height: 30.h,
                  color: Colors.red,
                ),
              ),
              Spacing.horizontalSpace(10),
            ],
          ),
          Divider(
            color: Colors.black,
            thickness: 2,
          ),
        ],
      ),
    );
  }
}
