import 'package:bagzz/core/helpers/spacing.dart';
import 'package:bagzz/core/theming/styles.dart';
import 'package:bagzz/core/widgets/buy_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class ProductDetails extends StatefulWidget {


  const ProductDetails({super.key});

  @override
  State<ProductDetails> createState() => _ProductDetailsState();
}

class _ProductDetailsState extends State<ProductDetails> {

  int _currentPage = 0;

  final PageController _pageController = PageController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            // Product image and details section
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(child: Image.asset('assets/images/bag1.png')),
      
                Spacing.horizontalSpace(10),
      
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Spacing.verticalSpace(10),
                      Text('Artsy', style: Styles.font22BlackBold),
                      Spacing.verticalSpace(4),
                      RichText(text: TextSpan(
                        style:TextStyle(
                          height: 1.3
                        ) ,
                        children: [
                          TextSpan(text: 'Wallet with chain \n', style: Styles.font14BlackMedium),
                          TextSpan(text: 'Style #36252 0YK0G 1000', style: Styles.font12Greylight),
                        ],
                      ),),
                      Spacing.verticalSpace(10),
                      Text('\$500', style: Styles.font18BlackBold),
                      Spacing.verticalSpace(15),
                      BuyButton(onPressed: (){}),
                      Spacing.verticalSpace(10),
                      Text('ADD TO CART', style: Styles.font14BlackMedium),
                      Divider(
                        color: Colors.black,
                        thickness: 2,
                        endIndent: 70.w,
                      ),
      
      
      
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 8, right: 8.0),
                  child: SvgPicture.asset('assets/svgs/fav.svg', height: 14.h),
                ),
              ],
            ),
      
            Spacing.verticalSpace(20),
            
            // Tab navigation
            Row(
              children: [
                _buildTab(0, 'Description'),
                Spacing.horizontalSpace(20),
                _buildTab(1, 'Shipping info'),
                Spacing.horizontalSpace(20),
                _buildTab(2, 'Payment options'),
              ],
            ),
            
            // Tab indicator line
            Container(
              height: 1,
              color: Colors.grey.withOpacity(0.3),
              margin: EdgeInsets.only(top: 8.h),
            ),
            
            // Tab content
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                children: [
                  _buildDescriptionTab(),
                  _buildShippingTab(),
                  _buildPaymentTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTab(int index, String title) {
    final isSelected = _currentPage == index;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _currentPage = index;
        });
        _pageController.animateToPage(
          index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      },
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              color: isSelected ? Colors.black : Colors.grey,
            ),
          ),
          if (isSelected)
            Container(
              height: 2,
              width: 50.w,
              margin: EdgeInsets.only(top: 8.h),
              color: Colors.black,
            ),
        ],
      ),
    );
  }

  Widget _buildDescriptionTab() {
    return Padding(
      padding: EdgeInsets.all(16.h),
      child: Text(
        'Pre-order, Made to Order and DIY items will ship on the estimated date noted on the product description page. These items will ship through Premium Express once they become available.',
        style: Styles.font14BlackMedium,
      ),
    );
  }

  Widget _buildShippingTab() {
    return Padding(
      padding: EdgeInsets.all(16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Standard shipping: 3-5 business days',
            style: Styles.font14BlackMedium,
          ),
          Spacing.verticalSpace(10),
          Text(
            'Express shipping: 1-2 business days',
            style: Styles.font14BlackMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentTab() {
    return Padding(
      padding: EdgeInsets.all(16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'We accept all major credit cards, PayPal, and Apple Pay.',
            style: Styles.font14BlackMedium,
          ),
          Spacing.verticalSpace(10),
          Text(
            'Payment is processed securely through our payment gateway.',
            style: Styles.font14BlackMedium,
          ),
        ],
      ),
    );
  }
}
