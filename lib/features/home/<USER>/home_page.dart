import 'package:bagzz/core/helpers/extentions.dart';
import 'package:bagzz/core/helpers/spacing.dart';
import 'package:bagzz/features/home/<USER>/widgets/button_text.dart';
import 'package:bagzz/features/home/<USER>/widgets/categorie_cart.dart';
import 'package:bagzz/features/home/<USER>/widgets/custom_grid_view.dart';
import 'package:bagzz/features/home/<USER>/widgets/item_cart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theming/styles.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  // advertisement - make static for better performance
  static const List<String> advertisement = [
    'assets/images/img.png',
    'assets/images/img1.png',
  ];
  int currentAdvertisementIndex = 0;

  // products - make static for better performance
  static const List<Map<String, String>> products = [
    {
      'name': 'Artsy',
      'img': 'assets/images/bag1.png',
    },
    {
      'name': 'Berkely',
      'img': 'assets/images/bag2.png',
    },
    {
      'name': 'Capucinus',
      'img': 'assets/images/bag3.png',
    },
    {
      'name': 'Monogram',
      'img': 'assets/images/bag4.png',
    },
  ];

  // categories - make static for better performance
  static const List<Map<String, String>> categories = [
    {
      'name': 'Handle bags',
      'img': 'assets/images/categories1.png',
    },
    {
      'name': 'Crossbody bags',
      'img': 'assets/images/categories2.png',
    },
    {
      'name': 'Shoulder bags',
      'img': 'assets/images/categories3.png',
    },
    {
      'name': 'Tote bag',
      'img': 'assets/images/categories4.png',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.all(10.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // advertisement - wrapped in RepaintBoundary for better performance
              RepaintBoundary(
                child: _buildAdvertisementSection(),
              ),
              Spacing.verticalSpace(20),

              // products view
              RepaintBoundary(
                child: CustomGridView(
                  products: products,
                ),
              ),

              Spacing.verticalSpace(20),
              // check all latest
              const Center(child: ButtonText()),

              Spacing.verticalSpace(20),
              // categories
              Text('Shop by categories', style: Styles.font24BlackBold),
              Spacing.verticalSpace(10),

              RepaintBoundary(
                child: CustomGridView(
                  products: categories,
                  itemBuilder: (context, index) {
                    return CategorieCart(
                      title: categories[index]['name'],
                      image: categories[index]['img'],
                    );
                  },
                ),
              ),

              Spacing.verticalSpace(20),
              const Center(child: ButtonText(
                text: 'Browse all categories',
              ),),
              Spacing.verticalSpace(20),


            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAdvertisementSection() {
    return Stack(
      children: [
        // advertisement image
        GestureDetector(
          onTap: () {
            context.pushNamed('product');
          },
          child: Container(
            clipBehavior: Clip.antiAlias,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Image.asset(
              advertisement[currentAdvertisementIndex],
              fit: BoxFit.fill,
              // Add caching for better performance
              cacheWidth: 400,
              cacheHeight: 200,
            ),
          ),
        ),
        
        // advertisement navigation buttons
        Positioned(
          bottom: 0,
          right: 22.w,
          child: _buildNavigationButton(
            icon: Icons.arrow_forward_ios,
            onTap: () => _changeAdvertisement(1),
          ),
        ),
        
        Positioned(
          bottom: 0,
          right: 75.w,
          child: _buildNavigationButton(
            icon: Icons.arrow_back_ios,
            onTap: () => _changeAdvertisement(-1),
          ),
        ),

        // advertisement text overlay
        Positioned(
          right: 30.w,
          top: 50.w,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildOverlayText('This'),
              _buildOverlayText('season\'s'),
              _buildOverlayText('latest'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNavigationButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 50.h,
        width: 50.w,
        color: Colors.black,
        child: Icon(icon, color: Colors.white),
      ),
    );
  }

  Widget _buildOverlayText(String text) {
    return Container(
      color: Colors.white,
      child: Text(
        text,
        style: Styles.font22BlackBold.copyWith(
          fontFamily: 'PlayfairDisplay',
        ),
      ),
    );
  }

  void _changeAdvertisement(int direction) {
    if (mounted) {
      setState(() {
        currentAdvertisementIndex = 
            (currentAdvertisementIndex + direction) % advertisement.length;
        if (currentAdvertisementIndex < 0) {
          currentAdvertisementIndex = advertisement.length - 1;
        }
      });
    }
  }
}
