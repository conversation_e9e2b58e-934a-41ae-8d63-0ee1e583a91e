import 'package:animated_notch_bottom_bar/animated_notch_bottom_bar/animated_notch_bottom_bar.dart';
import 'package:bagzz/core/helpers/extentions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:bagzz/core/helpers/spacing.dart';

import '../../../core/utils/styles.dart';
import 'home_page.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {

  List<Widget> pages=[
    HomePage(),
   //  SearchPage(),
   //  CartPage(),
   //  favoritePage(),
  ];
  int currentIndex=0;
  @override
  void initState() {
    super.initState();
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,


      appBar: AppBar(
        backgroundColor: Colors.white,
        title: Row(
          children: [
            GestureDetector(
              onTap: () {},
              child: SvgPicture.asset('assets/svgs/menu.svg'),
            ),
            Spacing.horizontalSpace(10),
            Text(
              'bagzz',
              style: Styles.font22BlackBold.copyWith(
                fontFamily: 'PlayfairDisplay',
              ),
            ),
            Spacer(),
            CircleAvatar(
              radius: 20.r,
              backgroundImage: AssetImage('assets/images/img1.png'),
            ),
          ],
        ),
      ),

      body: SafeArea(child: PageView(
        physics: NeverScrollableScrollPhysics(),
        controller: PageController(initialPage: currentIndex),
        onPageChanged: (value){
          setState(() {
            currentIndex=value;
          });
        },
        children: pages
      )
      ),

      bottomNavigationBar: AnimatedNotchBottomBar(notchBottomBarController: notchBottomBarController, bottomBarItems: bottomBarItems, onTap: onTap, kIconSize: kIconSize, kBottomRadius: kBottomRadius),
    );
  }
}
//bottomNavigationBar:  AnimatedNotchBottomBar(
//   pageController: _pageController,
//   bottomBarItems: [
//     const BottomBarItem(
//         inActiveItem: Icon(
//            Icons.home_filled,
//             color: Colors.blueGrey,
//         ),
//         activeItem: Icon(
//           Icons.home_filled,
//           color: Colors.blueAccent,
//         ),
//         itemLabel: 'Page 1',
//   ),
//     const BottomBarItem(
//         inActiveItem: Icon(
//             Icons.star,
//             color: Colors.blueGrey,
//           ),
//         activeItem: Icon(
//             Icons.star,
//             color: Colors.blueAccent,
//         ),
//         itemLabel: 'Page 2',
//   ),
//
//      ///svg item
//     BottomBarItem(
//         inActiveItem: SvgPicture.asset(
//           'assets/search_icon.svg',
//            color: Colors.blueGrey,
//        ),
//        activeItem: SvgPicture.asset(
//           'assets/search_icon.svg',
//            color: Colors.black,
//        ),
//        itemLabel: 'Page 3',
//     ),
//      ...
// )