import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theming/styles.dart';

class CategorieCart extends StatelessWidget {
  const CategorieCart({super.key, this.image, this.title});

  final String? image ;
  final String? title ;


  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(.15),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Image.asset(
              image ?? 'assets/images/categories1.png',
              height: 225.h,
              width: 170.w,
              fit: BoxFit.fill,
              // Add caching for better performance
              cacheWidth: 200,
              cacheHeight: 250,
            ),
          ),
          Positioned(
            bottom: 10.h,
            right: 0,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
              decoration: const BoxDecoration(
                color: Colors.black,
              ),
              child: Text(
                title ?? 'Handle bags',
                style: Styles.font16WhiteBold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
