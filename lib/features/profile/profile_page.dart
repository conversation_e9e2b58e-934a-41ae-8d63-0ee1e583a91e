import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/helpers/spacing.dart';
import '../../core/theming/styles.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,

      body: Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          children: [
            Row(
              children: [
                SizedBox(
                  height: 100.h,
                  width: 100.w,
                  child: Image.asset('assets/images/img1.png'),
                ),
                Spacing.horizontalSpace(10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('<PERSON>', style: Styles.font22BlackBold),
                    Text('<EMAIL>', style: Styles.font14BlackMedium),
                    Text('Edit Profile', style: Styles.font10BlackThin),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
