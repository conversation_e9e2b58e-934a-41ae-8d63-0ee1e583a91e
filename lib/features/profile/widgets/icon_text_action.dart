import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../core/helpers/spacing.dart';
import '../../../core/theming/styles.dart';

class IconTextAction  extends StatelessWidget {
  const IconTextAction({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          height: 40.h,
            width: 40.w,
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(.15),
              borderRadius: BorderRadius.circular(8.r),
              image: DecorationImage(
                image: AssetImage('assets/svgs/notepad.svg'),
              ),
            ),
            // child: SvgPicture.asset('assets/svgs/notepad.svg',height: 24.h,)
        ),
        Spacing.horizontalSpace(10),
        Text('My orders ',style: Styles.font14BlackMedium,),
        const Spacer(),
        IconButton(onPressed: (){}, icon: const Icon(Icons.arrow_forward_ios,size: 16,)),
      ],
    );
  }
}
