import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

import '../data/api_services.dart';
import '../data/models/saga_model.dart';

class SagaScreen extends StatefulWidget {
  const SagaScreen({super.key});

  @override
  State<SagaScreen> createState() => _SagaScreenState();
}

class _SagaScreenState extends State<SagaScreen> {
  @override
  void initState() {
    super.initState();
    _loadSaga();
  }

  List<SagaModel> sagas = [];

  Future<void> _loadSaga() async{
    sagas = await ApiServices.fetchSaga();
    setState(() {
      sagas = sagas;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('One Piece Sagas')),
      body: ListView.builder(
        itemCount: sagas.length,
        itemBuilder: (context, index) {
          final saga = sagas[index];
          return ListTile(
            title: Text(saga.name),
            subtitle: Text(
              'Saga Number: ${saga.sagaNumber}\n'
              'Volumes: ${saga.sagaVolume}\n'
              'Chapters: ${saga.sagaChapiter}\n'
              'Episodes: ${saga.sagaEpisode}',
            ),
          );
        },
      ),
    );
  }
}
