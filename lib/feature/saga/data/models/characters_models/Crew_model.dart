class CrewModel {
  final int id;
  final String name;
  final String description;
  final String status ;
  final String number ;
  final String totalPrime;
  final bool isYonko;

  CrewModel({
    required this.id,
    required this.name,
    required this.description,
    required this.status,
    required this.number,
    required this.totalPrime,
    required this.isYonko,
  });

  factory CrewModel.fromJson(Map<String, dynamic> json) {
    return CrewModel(
      id: json['id'],
      name: json['name'],
      description: json['description']?? 'Unknown',
      status: json['status'],
      number: json['number'],
      totalPrime: json['total_prime'],
      isYonko: json['is_yonko'],
    );
  }


}