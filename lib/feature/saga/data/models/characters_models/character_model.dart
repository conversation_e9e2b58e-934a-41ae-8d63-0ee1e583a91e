import 'package:one_piece/feature/saga/data/models/characters_models/Crew_model.dart';

class CharacterModel {
  final int id;
  final String name;
  final String height;
  final String age;
  final CrewModel crew;
  final Fruit fruit;
  final String jop;
  final String status;

  CharacterModel({
    required this.id,
    required this.name,
    required this.image,
  });

  factory CharacterModel.fromJson(Map<String, dynamic> json) {
    return CharacterModel(
      id: json['id'],
      name: json['name'],
      image: json['image'],
    );
  }
}
