class FruitModel {
  final int id;
  final String name;
  final String image;
  final String description;
  final String type;


  FruitModel({
    required this.id,
    required this.name,
    required this.image,
    required this.description,
    required this.type,
  });

  factory FruitModel.fromJson(Map<String, dynamic> json) {
    return FruitModel(
      id: json['id'],
      name: json['name'],
      image: json['filename'],
      description: json['description'],
      type: json['type'],
    );
  }
}
