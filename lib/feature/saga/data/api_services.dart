import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:one_piece/feature/saga/data/api_constant.dart';

import 'models/saga_model.dart';

class ApiServices {

  static Future<List<SagaModel>> fetchSaga() async {
    final response = await http.get(
      Uri.parse(ApiConstant.baseUrlOfSagas),
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data.map((json) => SagaModel.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load sagas');
    }
  }

  Future<List<SagaModel>> fetchSagaById(int id) async {
    final response = await http.get(
      Uri.parse('https://api.api-onepiece.com/v2/sagas/en/$id'),
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data.map((json) => SagaModel.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load sagas');
    }
  }
}