class SagaModel {
  final int id;
  final String name;
  final String sagaNumber;
  final String sagaChapiter;
  final String sagaVolume;
  final String sagaEpisode;

  SagaModel({
    required this.id,
    required this.name,
    required this.sagaNumber,
    required this.sagaChapiter,
    required this.sagaVolume,
    required this.sagaEpisode,
  });

  factory SagaModel.fromJson(Map<String, dynamic> json) {
    return SagaModel(
      id: json['id'],
      name: json['title'],
      sagaNumber: json['saga_number'],
      sagaChapiter: json['saga_chapitre'],
      sagaVolume: json['saga_volume'],
      sagaEpisode: json['saga_episode'],
    );
  }
}
