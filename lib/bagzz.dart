import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'features/home/<USER>/home_screen.dart';

class Bagzz extends StatelessWidget {
  const Bagzz({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 808),
      minTextAdapt: true,
      splitScreenMode: true,
      // Optimize ScreenUtil initialization
      ensureScreenSize: true,
      // Use builder only if you need to use library outside ScreenUtilInit context
      builder: (_, child) {
        return MaterialApp(
          debugShowCheckedModeBanner: false,
          title: 'Bagzz',
          // Optimize theme creation
          theme: _buildTheme(),
          home: const HomeScreen(),
          // Add performance optimizations
          builder: (context, child) {
            return MediaQuery(
              // Disable text scaling for consistent UI
              data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
              child: child!,
            );
          },
        );
      },
    );
  }

  ThemeData _buildTheme() {
    return ThemeData(
      primarySwatch: Colors.blue,
      textTheme: Typography.englishLike2018.apply(fontSizeFactor: 1.sp),
      // Add performance optimizations
      visualDensity: VisualDensity.adaptivePlatformDensity,
      // Reduce animation duration for faster UI
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        },
      ),
    );
  }
}
